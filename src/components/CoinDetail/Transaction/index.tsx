import React, { useEffect } from 'react';
import { useState } from 'react';
import { View, Text, ScrollView, Platform } from 'react-native';
import { BetterImage, Touch } from '@xmly/rn-components';
import { getStyles } from './styles';
import { useAtomValue, useSetAtom } from 'jotai';
import bannerThemeAtom from './theme';
import { coinInfoAtom, updateCoinDetailAtom } from '../atom';
import ConfirmModal from './ConfirmModal';
import { BannerCard } from 'services/welfare/types';
import { exchangeGoldCoin } from 'services/welfare';
import { Toast } from '@xmly/rn-sdk';
import { TransactionType } from 'constants/ad';
import { balanceAtom, updateWelfareAtom } from 'atom/welfare';
import xmlog from 'utilsV2/xmlog';
import { themeAtom } from 'atom/theme';

export default function Transaction() {

  const { bannerCards } = useAtomValue(coinInfoAtom);
  const theme = useAtomValue(bannerThemeAtom);
  const OSTheme = useAtomValue(themeAtom);
  const styles = getStyles(theme);
  const [selectedCard, setSelectedCard] = useState<BannerCard>();
  const [modalVisible, setModalVisible] = useState(false);
  const updateCoinDetail = useSetAtom(updateCoinDetailAtom);
  const { balance } = useAtomValue(coinInfoAtom);
  const updateWelfare = useSetAtom(updateWelfareAtom);
  const [sortedBannerCards, setSortedBannerCards] = useState<BannerCard[]>([]);
  const [renderKey, setRenderKey] = useState(0);

  // 添加强制更新函数
  const forceRerender = () => {
    setRenderKey(prev => prev + 1);
  };

useEffect(() => {
  const sorted = [...bannerCards].sort((a, b) => {
    if (a.status === 1 && b.status === 2) return -1;
    if (a.status === 2 && b.status === 1) return 1;
    return 0;
  });
  console.log("sortedBannerCards= before", bannerCards);
  console.log("sortedBannerCards= after", sorted);
  // 调试 status=2 的卡片
  sorted.forEach((card, index) => {
    if (card.status === 2) {
      console.log(`Card ${index} (status=2):`, {
        title: card.title,
        status: card.status,
        statusIcon: card.statusIcon,
        statusIconType: typeof card.statusIcon,
        statusIconLength: card.statusIcon?.length,
        hasStatusIcon: !!card.statusIcon,
        isNotEmpty: card.statusIcon && card.statusIcon.trim() !== '',
        willShow: card.status === 2 && card.statusIcon,
        // 添加更多调试信息
        statusIconTruthy: Boolean(card.statusIcon),
        statusIconFalsy: !card.statusIcon,
        statusIconEmptyString: card.statusIcon === '',
        statusIconNull: card.statusIcon === null,
        statusIconUndefined: card.statusIcon === undefined
      });
    }
  });
  // 使用 setTimeout 确保状态更新在下一个事件循环中执行
  setTimeout(() => {
    setSortedBannerCards(sorted);
    // 强制重新渲染以确保状态同步
    forceRerender();
  }, 0);
}, [bannerCards]);
console.log("sortedBannerCards== before2 ", sortedBannerCards);
  const Image = "https://imagev2.xmcdn.com/storages/b333-audiofreehighqps/E5/60/GAqh9sAL9y1XAAAd1wOs349q.png";

  const handleCardPress = (card: BannerCard) => {
    // 任务中心_收支兑换明细-金币换XX入口  点击事件
    xmlog.click(67692, '', { currPage: 'point_exchange', title: card.title, titleType: card.consumeType === 1 ? '畅听' : '提现', dialogTitle: card.title, from: '福利中心' });
    if (balance < card.coins) {
      Toast.info('金币不足');
      return;
    }
    setSelectedCard(card);
    setModalVisible(true);
  };
  
  const handleConfirm = async () => {
    if (!selectedCard) return;

      try {
        const response = await exchangeGoldCoin({
          consumeType: selectedCard.consumeType,
          coins: selectedCard.coins,
        });
  
        if (response?.data?.success) {
          Toast.info(response?.data?.toast || '兑换成功');
          updateCoinDetail({ type: TransactionType.EXPENSE, refresh: true });
          updateWelfare();
        } else if (response?.data?.toast) {
          Toast.info(response.data.toast);
          updateCoinDetail({ type: TransactionType.EXPENSE, refresh: true });
        }
      } catch (error) {
        console.error('Exchange failed:', error);
        Toast.info('系统繁忙，请稍后再试');
      } finally {
        setModalVisible(false);
      } 

  };

  useEffect(() => {
    bannerCards.forEach((card) => {
      // 任务中心_收支兑换明细-金币换XX入口  控件曝光
      xmlog.event(67693, 'slipPage', { currPage: 'point_exchange', title: card.title, titleType: card.consumeType === 1 ? '畅听' : '提现', dialogTitle: card.title, from: '福利中心' });
    });
  }, [bannerCards.length]);

  return (
    <>
      <View style={styles.container}>
      {sortedBannerCards.length === 1 ? (
        // 单个卡片展示
        <Touch
          style={styles.card}
          onPress={() => handleCardPress(sortedBannerCards[0])}
        >
          <Text style={styles.title}>{sortedBannerCards[0].title}</Text>
          <View style={styles.valueContainer}>
            <Text style={styles.value}>需{sortedBannerCards[0].coins}金币兑换</Text>
            <BetterImage
              source={{ uri: theme.icon }}
              style={styles.icon}
              imgHeight={8}
              imgWidth={5}
            />
          </View>
        </Touch>
      ) : sortedBannerCards.length === 2 ? (
        // 两个卡片展示
        <View style={styles.cardrowTwo}>
          {sortedBannerCards.map((card, index) => (
            <Touch
              key={card.title}
              style={[
                styles.cardItemTwo
              ]}
            >
                <Text style={[styles.title,styles.titleMargin]}>{card.title}</Text>
                <Text style={styles.subTitle}>
                  {card.lineCoins ? (
                    <>
                      <Text style={[styles.value, styles.lineCoins, OSTheme === 'dark' && styles.lineCoinsDark]}>
                        {card.lineCoins}
                      </Text>
                      <Text style={styles.value}>{' '}</Text> 
                      <Text style={styles.value}>
                         {card.subTitle.split(/(\d+)/).map((part, index) => (
                          <Text
                            key={index}
                            style={/\d+/.test(part) ? [styles.value, styles.redNumber] : styles.value}
                          >
                             {part}
                          </Text>
                        ))}
                      </Text>
                    </>
                  ) : (
                    <Text style={styles.value}>{card.subTitle}</Text>
                  )}
                </Text>
                <Touch 
                  style={[styles.exchangeButton, card.status === 2 ? styles.exchangeButtonDisabled : styles.exchangeButtonColor]}
                  onPress={() => card.status !== 2 && handleCardPress(card)}
                >
                  <Text 
                    style={[
                      styles.exchangeButtonText,
                      card.status === 2 ? styles.exchangeButtonTextDisabled : styles.exchangeButtonTextColor,
                      Platform.OS === 'ios' && styles.exchangeButtonTextIOS
                    ]}
                  >
                    {card.buttonText || (card.status === 2 ? '已抢光' : '立即兑换')}
                  </Text>
                  {card.status !== 2 && card.buttonIcon && (
                    <BetterImage
                      source={{ uri: card.buttonIcon }}
                      style={styles.buttonIcon}
                      imgHeight={14}
                      imgWidth={24}
                    />
                  )}
                </Touch>
                {card.status === 2 && card.statusIcon && (
                  <BetterImage
                    key={`${card.title}-${card.statusIcon}-${renderKey}`}
                    source={{ uri: card.statusIcon }}
                    style={styles.statusImage}
                    imgHeight={44}
                    imgWidth={44}
                    onLoad={() => console.log(`StatusIcon loaded successfully for card: ${card.title}`)}
                    onError={() => console.log(`StatusIcon load failed for card: ${card.title}`)}
                  />
                )}
            </Touch>
          ))}
        </View>
      ) : (
        // 三个或更多卡片展示
        <ScrollView 
          horizontal={true}
          showsHorizontalScrollIndicator={false}
        >
          <View style={styles.cardrow}>
            {sortedBannerCards.map((card, index) => (
              <Touch
                key={card.title}
                style={[
                  styles.cardItem,
                  index === 0 && styles.cardItemFirst,
                  index === sortedBannerCards.length - 1 && styles.cardItemLast
                ]}
              >
                <Text style={[styles.title,styles.titleMargin]}>{card.title}</Text>
                <Text style={styles.subTitle}>
                  {card.lineCoins ? (
                    <>
                      <Text style={[styles.value, styles.lineCoins, OSTheme === 'dark' && styles.lineCoinsDark]}>{card.lineCoins}</Text>
                      <Text style={styles.value}>{' '}</Text> 
                      <Text style={styles.value}>
                         {card.subTitle.split(/(\d+)/).map((part, index) => (
                          <Text
                            key={index}
                            style={/\d+/.test(part) ? [styles.value, styles.redNumber] : styles.value}
                          >
                             {part}
                          </Text>
                        ))}
                      </Text>
                    </>
                  ) : (
                    <Text style={[styles.value]}>{card.subTitle}</Text>
                  )}
                </Text>
                <Touch 
                  style={[styles.exchangeButton, card.status === 2 && styles.exchangeButtonDisabled]}
                  onPress={() => card.status !== 2 && handleCardPress(card)}
                >
                  <Text 
                    style={[
                      styles.exchangeButtonText,
                      card.status === 2 && styles.exchangeButtonTextDisabled
                    ]}
                  >
                    {card.buttonText || (card.status === 2 ? '已抢光' : '立即兑换')}
                  </Text>
                  {card.status !== 2 && card.buttonIcon && (
                    <BetterImage
                      source={{ uri: card.buttonIcon }}
                      style={styles.buttonIcon}
                      imgHeight={14}
                      imgWidth={24}
                    />
                  )}
                </Touch>
                {card.status === 2 && card.statusIcon && (
                  <BetterImage
                    key={`${card.title}-${card.statusIcon}-${renderKey}`}
                    source={{ uri: card.statusIcon }}
                    style={styles.statusImage}
                    imgHeight={44}
                    imgWidth={44}
                    onLoad={() => console.log(`StatusIcon loaded successfully for card: ${card.title}`)}
                    onError={() => console.log(`StatusIcon load failed for card: ${card.title}`)}
                  />
                )}
              </Touch>
            ))}
          </View>
        </ScrollView>
      )}
      </View>
      <ConfirmModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        onConfirm={handleConfirm}
        card={selectedCard}
      />
    </>
  );
} 