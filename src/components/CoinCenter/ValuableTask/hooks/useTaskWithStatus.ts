import { useCallback, useEffect, useRef, useState } from "react";
import { AdAppTaskStatus, AdDPTaskItem } from "typesV2/adDPTask";
import { valuableTaskAtom, ValuableTaskInfo, updateValuableTask<PERSON>tom, writeValuableTask<PERSON>tom } from "../store";
import { useAtomValue, useSetAtom } from "jotai";
import { adAppClickReport } from "servicesV2/requestAdDPTaskList";
import { checkAppInstalled, openAdApp } from "components/AdAppTask/utils";
import { Toast } from "@xmly/rn-sdk";
import { addMessageAtom } from "atom/push";
import xmlog from "utilsV2/xmlog";
import { rewardGoldCoin } from "services/welfare";
import adDPTaskCtrl from "utilsV2/adDPTask";
import { RewardType } from "constants/ad";
import { AppState, AppStateStatus } from "react-native";
import log from "utils/log";
import useRewardGoldCoin from 'hooks/useRewardGoldCoin';
import { AD_SOURCE } from "constants/ad";
import { FallbackReqType } from "constants/ad";

const btnTexts = ['去下载', '去打开', '领奖励', '结算中'];

export default function useTaskWithStatus() {
  const taskInfo = useAtomValue(valuableTaskAtom) as ValuableTaskInfo;
  const responseId = taskInfo?.responseId || 0;
  const installed = taskInfo?.installed;
  const requestId = taskInfo?.requestId;
  const task = taskInfo?.data?.[0];
  const taskStatus = task?.taskStatus || AdAppTaskStatus.Default;
  const { dpMarketUrl = '', appPackageName = '', adid = 0, businessExtraInfo } = (task as AdDPTaskItem) || {};
  const [btnText, setBtnText] = useState('去完成');
  const updateValuableTask = useSetAtom(updateValuableTaskAtom);
  const valuableTaskInfo = useAtomValue(valuableTaskAtom) as ValuableTaskInfo;
  const getTask = useSetAtom(writeValuableTaskAtom);
  const updateTask = useSetAtom(updateValuableTaskAtom);
  const rewardGoldCoin = useRewardGoldCoin();

  async function reward() {
    const commonParams = JSON.parse(task!.commonReportMap);
    try {
      await rewardGoldCoin({ //TODO: 页面恢复弹奖励Toast
        rewardType: RewardType.VALUABLE,
        coins: 0,
        sourceName: AD_SOURCE.VALUABLE,
        adId: task!.adid,
        adResponseId: responseId,
        ecpm: commonParams?.adxRtbSettlementPrice || '',
        encryptType: commonParams?.encryptType || '',
        fallbackReq: FallbackReqType.NORMAL,
        extMap: JSON.stringify({
           pullNewTask: dpMarketUrl ? 1 : 0
        })
      });
    } catch (error) {
      console.log('error', error);
    }
    getTask();
  }

  const clickReport = useCallback(function clickReport() {
    xmlog.click(67695, 'ValuableTask', {
      taskId: `${adid}`,
      taskTitle: task?.name || '',
      moduleTitle: '惊喜任务',
      currPage: 'welfareCenter',
      Item: btnText,
      position: '1',
      responseId: `${responseId}`
    })
  }, [adid, task?.name, responseId, btnText]);

  async function onClick() {
    clickReport();
    adDPTaskCtrl.onExpo({ adItem: task!, responseId: valuableTaskInfo?.responseId! })
    updateValuableTask({ exposed: true });
    let isDownload = false;
    if (taskStatus === AdAppTaskStatus.Finished) {
      reward();
      await adAppClickReport({ responseId, adId: adid, type: 'click' });
    } else {
      if (dpMarketUrl && !(businessExtraInfo?.bannerMiniProgram || businessExtraInfo?.bannerLiveBroadcast)) { // 拉新任务，先检查是否安装
        const result = await checkAppInstalled(appPackageName!);
        await adAppClickReport({ responseId, adId: adid, installed: result, type: 'click' });
        getTask();
        if (!result) {
          isDownload = true;
          updateTask({ installed: false });
        } else {
          updateTask({ installed: true });
        }
      }
      const result = await openAdApp({ isDownload, adItem: task!, responseId: `${responseId}` });
      const clientCode = String(result?.clientCode);
      if (isDownload && !(businessExtraInfo?.bannerMiniProgram || businessExtraInfo?.bannerLiveBroadcast)) { // 去下载
        if (clientCode === '3') {
          Toast.info('下载失败');
        }
        return;
      }
      // 去打开
      switch (clientCode) {
        case '0': // 跳转时长不足
          Toast.info('浏览时长不足');
          if (dpMarketUrl) {
            await adAppClickReport({ responseId, adId: adid, jump3s: false, installed: true, type: 'result' });
            getTask();
          }
          break;
        case '1':
          Toast.info('任务完成');
          if (dpMarketUrl) { // 拉新任务，记录完成时间
            await adAppClickReport({ responseId, adId: adid, jump3s: true, installed: true, type: 'result' });
            getTask();
          } else { // 拉活任务直接领取奖励
            reward();
          }
          break;
        case '3': // 其他原因失败
        default:
          if (dpMarketUrl) {
            await adAppClickReport({ responseId, adId: adid, jump3s: false, installed: true, type: 'result' });
            getTask();
          }
          Toast.info('任务未完成');
      }
    }
  }

  async function resumeListener(state: AppStateStatus) {
    log('debug__resumeListener', state);
    if (state === 'active') {
      getTask();
    }
  }

  async function checkStatus() {
    switch (taskStatus) {
      case AdAppTaskStatus.Finished: // 已完成
        setBtnText(task?.buttonText || btnTexts[2]);
        break;
      case AdAppTaskStatus.Settling: // 结算中
        if (installed) {
          setBtnText(task?.buttonText || btnTexts[3]);
          break;
        }
      default:
        if (dpMarketUrl) {
          if (installed) {
            return setBtnText(task?.buttonText || btnTexts[1]); // 去打开
          }
          if(businessExtraInfo?.bannerMiniProgram || businessExtraInfo?.bannerLiveBroadcast){
            return setBtnText(task?.buttonText || btnTexts[1])
          }
          setBtnText(btnTexts[0]); // 去下载
        } else { // 拉活任务展示去打开
          setBtnText(task?.buttonText || btnTexts[1]);
        }
    }
  }

  useEffect(() => {
    checkStatus();
  }, [installed, requestId, taskStatus])

  useEffect(() => {
    if (dpMarketUrl && taskStatus !== undefined && taskStatus !== AdAppTaskStatus.Finished) {
      AppState.addEventListener('change', resumeListener);
    }

    return () => {
      AppState.removeEventListener('change', resumeListener);
    }
  }, [dpMarketUrl, taskStatus]);

  return {
    btnText,
    desc: task?.description,
    onClick,
    taskStatus,
  }
} 