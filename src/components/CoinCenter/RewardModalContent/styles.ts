import { StyleSheet, Platform } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";

const amountBg = 'https://imagev2.xmcdn.com/storages/68c8-audiofreehighqps/A1/D3/GKwRIW4MOm1QAAAC6wPXXxCV.png'
export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({

  contentContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
  },
  rewardContainer: {
    alignItems: 'center',
  },
  popupImageContainer: {
    width: px(241),
    aspectRatio: 241 / 293,
    alignItems: 'center',
  },
  popupImage: {
    width: px(241),
    aspectRatio: 241 / 293,
    position: 'absolute',
    left: 0,
    top: 0,
  },
  coinIcon: {
    width: px(87),
    aspectRatio: 87 / 68,
  },
  amountContainer: {
    position: 'absolute',
    top: px(137),
    left: px(160),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  amountTextContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: px(22),
    backgroundColor: 'rgba(255, 110, 104, 0.14)',
    borderRadius: px(8),
    paddingHorizontal: px(4),
  },
  amountText: {
    height: px(19),
    fontSize: px(16),
    lineHeight: px(20),
    color: theme.amountTextColor,
    fontFamily: 'XmlyNumber',
    fontWeight: 'bold',
  },
  amountUnit: {
    width: px(12),
    height: px(16),
    fontSize: px(12),
    lineHeight: px(16),
    color: theme.amountTextColor,
    fontFamily: 'Microsoft YaHei',
    fontWeight: 'bold',
  },
  triangle: {
    width: 0,
    height: 0,
    borderTopWidth: px(2),
    borderLeftWidth: px(4),
    borderRightWidth: px(2),
    borderBottomWidth: px(2),
    borderTopColor: 'rgba(255, 110, 104, 0.14)',
    borderLeftColor: 'rgba(255, 110, 104, 0.14)',
    borderRightColor: 'transparent',
    borderBottomColor: 'transparent',
    marginTop: px(-0.15),
  },
  congratsText: {
    fontSize: px(22),
    lineHeight: px(28),
    color: theme.congratsTextColor,
    marginBottom: px(7),
    marginTop: px(30),
    fontWeight: 'bold',
    fontFamily: 'Microsoft YaHei',
  },
  subTitleText: {
    fontSize: px(11),
    lineHeight: px(15),
    color: theme.subTitleTextColor,
    marginBottom: px(14),
    textAlign: 'center',
  },
  coinsText: {
    marginTop: px(-3),
    fontSize: px(38),
    fontWeight: 'bold',
    color: '#FF4444',
    fontFamily: 'XmlyNumber',
  },
  confirmButton: {
    position: 'absolute',
    bottom: 30,
  },
});