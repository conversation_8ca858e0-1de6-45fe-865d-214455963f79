import React from "react";
import { View, Animated, Text, Image, ImageBackground } from "react-native";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import rewardModalContentThemeAtom, { darkTheme, lightTheme } from "./theme";
import { preloadImages } from "utils/preloadImages";
import { themeAtom } from "atom/theme";
import ConfirmButton from "components/CoinCenter/common/ConfirmButton";

// 导出金币图片以便预加载
export const COINS_IMAGE = 'https://imagev2.xmcdn.com/storages/99ff-audiofreehighqps/8C/1E/GKwRIRwL05RWAAA0tAOXmR1U.png';
const amountBg = 'https://imagev2.xmcdn.com/storages/68c8-audiofreehighqps/A1/D3/GKwRIW4MOm1QAAAC6wPXXxCV.png'
export const useLotteryImages = () => {
  const theme = useAtomValue(themeAtom);
  const PRELOAD_IMAGES = theme === 'dark' ? [
    darkTheme.popupImage,
    COINS_IMAGE,
  ] : [
    lightTheme.popupImage,
    COINS_IMAGE,
  ];

  return () => preloadImages(PRELOAD_IMAGES);
};

// 导出所有需要预加载的图片
export const PRELOAD_IMAGES = [
  COINS_IMAGE,
  darkTheme.popupImage,
  lightTheme.popupImage
];

interface RewardModalContentProps {
  coins: number;
  btnText?: string;
  title?: string;
  subTitle?: string;
  onPress: () => void;
  scaleAnim?: Animated.Value;
}

export default function RewardModalContent({
  coins,
  btnText = '确定',
  title = '瓜分任务开奖',
  subTitle = '在上轮瓜分任务中获得',
  scaleAnim,
  onPress
}: RewardModalContentProps) {
  const theme = useAtomValue(rewardModalContentThemeAtom);
  const styles = getStyles(theme);

  const ratio = 33000;
  const calculateAmount = (coins: number) => {
    if (coins % ratio === 0) {
      return coins / ratio;
    } else  {
      let result = (coins / ratio).toString().padEnd(2, '0');
      return parseFloat(result).toFixed(1);
    }
  }
  const amount = calculateAmount(coins);
  const approximatelyEqual = '≈';
  // const scaleAnim = useRef(new Animated.Value(0)).current;
  return (
    <View style={styles.contentContainer}>
      <Animated.View
        style={[
          styles.popupImageContainer,
          {
            transform: [
              { scale: scaleAnim || 1 }
            ]
          }
        ]}
      >
        {/* Image组件可以直接使用预加载资源 */}
        <Image
          source={{ uri: theme.popupImage }}
          style={styles.popupImage}
        />

        <View style={styles.rewardContainer}>
          <Text style={styles.congratsText}>{title}</Text>
          {subTitle && <Text style={styles.subTitleText}>{subTitle}</Text>}
          <Image
            source={{ uri: COINS_IMAGE }}
            style={styles.coinIcon}
          />
          <Text style={styles.coinsText}>+{coins}</Text>
        </View>
        <View style={styles.amountContainer}>
          <View style={styles.amountTextContainer}>
            <Text style={styles.amountText}>{approximatelyEqual + amount}</Text>
            <Text style={styles.amountUnit}>元</Text>
          </View>
          <View style={styles.triangle}></View>
        </View>

        <ConfirmButton
          text={btnText}
          onPress={onPress}
          style={styles.confirmButton}
        />
      </Animated.View>
    </View>
  );
}