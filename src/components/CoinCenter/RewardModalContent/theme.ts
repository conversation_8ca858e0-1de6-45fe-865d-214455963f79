import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  congratsTextColor: '#FFFFFF',
  popupImage:'https://imagev2.xmcdn.com/storages/aa59-audiofreehighqps/00/CD/GKwRIJELzkB7AABqLwOUat8G.png',
  subTitleTextColor: 'rgba(255, 255, 255, 0.5)',
  amountTextColor: 'rgba(255, 110, 104, 0.74)',
};

export const lightTheme = {
  backgroundColor: 'rgba(0, 0, 0, 0.7)',
  congratsTextColor: '#2C2C3C',
  popupImage:'https://imagev2.xmcdn.com/storages/1ecc-audiofreehighqps/A1/F8/GKwRIasLzjf-AABpEwOUZXCk.png',
  subTitleTextColor: 'rgba(44, 44, 60, 0.5)',
  amountTextColor: '#FF6E68',
};

const lotteryModalThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default lotteryModalThemeAtom; 