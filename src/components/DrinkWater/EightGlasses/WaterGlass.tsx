import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ImageBackground } from 'react-native';
import { Toast } from '@xmly/rn-sdk';
import { WaterInfo, WaterStatus } from 'services/welfare/drinkWater';
import { AD_SOURCE, RewardType, LISTEN_TASK_POSITION, FallbackReqType } from 'constants/ad';
import watchAd from 'utils/watchAd';
import useRewardGoldCoin from 'hooks/useRewardGoldCoin';
import { getStyles } from './WaterGlass.styles';
import waterGlassCompleted from 'appImagesV2/water_glass_completed';
import waterGlassMakeup from 'appImagesV2/water_glass_makeup';
import waterGlassPending from 'appImagesV2/water_glass_pending';
import waterGlassBlur from 'appImagesV2/water_glass_blur';
import useAppStateToast from "hooks/useAppStateToast";
import DrinkWaterModal from '../Modal';
import drinkWaterMakeUpStorage from '../../../storageV2/drinkWaterMakeUpStorage'
import xmlog from 'utilsV2/xmlog'

interface WaterGlassProps {
  waterInfo: WaterInfo;
  onRefresh: () => void;
  xmRequestId: string;
  coldTime?: number; // 补卡冷却时间，单位：秒，可选参数
}

export default function WaterGlass({ waterInfo, onRefresh, xmRequestId, coldTime }: WaterGlassProps) {
  const styles = getStyles();
  const rewardGoldCoin = useRewardGoldCoin();
  const { showToast } = useAppStateToast();

  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'withAd' | 'normal'>('normal');
  const [modalCoins, setModalCoins] = useState(0);
  const [modalUpgradeCoins, setModalUpgradeCoins] = useState(0);

  // 获取状态对应的背景图标和底部文案
  const getStatusInfo = () => {
    switch (waterInfo.status) {
      case WaterStatus.PENDING:
        return {
          bottomText: `${waterInfo.coins}金币`,
          bottomTextStyle: [styles.bottomText, styles.coinsText],
          backgroundImage: waterGlassPending,
          clickable: true,
          showOverlay: false,
        };
      case WaterStatus.NOT_STARTED:
        return {
          bottomText: `${waterInfo.coins}金币`,
          bottomTextStyle: [styles.bottomText, styles.coinsText],
          backgroundImage: waterGlassPending,
          clickable: false,
          showOverlay: true,
          overlayImage: waterGlassBlur,
          timeAreaText: waterInfo.timeArea,
        };
      case WaterStatus.COMPLETED:
        return {
          bottomText: '已打卡',
          bottomTextStyle: [styles.bottomText, styles.completedText],
          backgroundImage: waterGlassCompleted,
          clickable: false,
          showOverlay: false,
        };
      case WaterStatus.MAKE_UP:
        return {
          bottomText: '补打卡',
          bottomTextStyle: [styles.bottomText, styles.makeUpText],
          backgroundImage: waterGlassMakeup,
          clickable: true,
          showOverlay: false,
        };
      default:
        return {
          bottomText: `${waterInfo.coins}金币`,
          bottomTextStyle: [styles.bottomText, styles.coinsText],
          backgroundImage: waterGlassPending,
          clickable: false,
          showOverlay: false,
        };
    }
  };

  // 处理正常打卡
  const handleNormalCheckIn = async () => {
    try {
      xmlog.click(69119, undefined, {
        currPage: 'drinkWaterActivityPage',
        Item: `第${waterInfo.index}杯水打卡`,
        xmRequestId,
      })
      const result = await rewardGoldCoin({
        rewardType: RewardType.DRINK_WATER_NORMAL,
        sourceName: AD_SOURCE.DRINK_WATER_NORMAL,
        coins: waterInfo.coins,
        fallbackReq: FallbackReqType.NORMAL,
        index: waterInfo.index,
      },true,true);

      if (result?.success) {
        // 根据upgradeCoins字段决定显示哪种弹窗
        if (result.upgradeCoins && result.upgradeCoins > 0) {
          // 显示膨胀弹窗
          setModalType('withAd');
          setModalCoins(waterInfo.coins);
          setModalUpgradeCoins(result.upgradeCoins);
          setModalVisible(true);
        } else {
          // 显示普通奖励弹窗
          setModalType('normal');
          setModalCoins(waterInfo.coins);
          setModalUpgradeCoins(0);
          setModalVisible(true);
        }
        // 移除立即调用onRefresh，改为在弹窗关闭时调用
        // onRefresh();
      } else {
        showToast('打卡失败，请稍后重试');
      }
    } catch (error) {
      Toast.info('打卡失败，请稍后重试');
    }
  };

  // 处理补打卡（需要看广告）
  const handleMakeUpCheckIn = async () => {
    // 先判断本地补打卡时间间隔
    const lastMakeUpTime = await drinkWaterMakeUpStorage.get();
    const now = Date.now();
    // 使用动态配置的冷却时间，如果没有配置则默认5分钟
    const interval = (coldTime || 5 * 60) * 1000; // 转换为毫秒
    if (lastMakeUpTime && now - lastMakeUpTime < interval) {
      xmlog.click(69119, undefined, {
        currPage: 'drinkWaterActivityPage',
        Item: `第${waterInfo.index}杯水补卡时间未到`,
        xmRequestId,
      })
      const remain = interval - (now - lastMakeUpTime);
      const min = Math.floor(remain / 60000);
      const sec = Math.floor((remain % 60000) / 1000);
      showToast(`${min > 0 ? min + '分' : ''}${sec}秒后可继续补卡哦~`);
      return;
    }
    try {
      xmlog.click(69119, undefined, {
        currPage: 'drinkWaterActivityPage',
        Item: `第${waterInfo.index}杯水补卡`,
        xmRequestId,
      })
      const adResult = await watchAd({
        sourceName: AD_SOURCE.DRINK_WATER_AGAIN,
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.DRINK_WATER_AGAIN,
        coins: waterInfo.coins,
        rewardVideoStyle: 0,
      });

      if (adResult.success) {
        const result = await rewardGoldCoin({
          rewardType: RewardType.DRINK_WATER_AGAIN,
          sourceName: AD_SOURCE.DRINK_WATER_AGAIN,
          coins: waterInfo.coins,
          adId: adResult.adId,
          adResponseId: adResult.adResponseId,
          encryptType: adResult.encryptType,
          ecpm: adResult.ecpm,
          fallbackReq: adResult.fallbackReq ?? FallbackReqType.NORMAL,
          index: waterInfo.index,
        },true, true);

        if (result?.success) {
          showToast("补卡成功")
          await drinkWaterMakeUpStorage.set(now); // 记录补打卡时间
          onRefresh();
        } else {
          showToast("补卡失败，请稍后重试")
        }
      }
    } catch (error) {
      showToast("补卡失败，请稍后重试")
    }
  };

  // 处理膨胀弹窗中的广告观看
  const handleAdReward = async () => {
    try {
      const adResult = await watchAd({
        sourceName: AD_SOURCE.DRINK_WATER_DOUBLE_REWARD,
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.DRINK_WATER_DOUBLE_REWARD,
        coins: modalUpgradeCoins,
        rewardVideoStyle: 0,
      });

      if (adResult.success) {
        const result = await rewardGoldCoin({
          rewardType: RewardType.DRINK_WATER_DOUBLE_REWARD,
          sourceName: AD_SOURCE.DRINK_WATER_DOUBLE_REWARD,
          coins: modalUpgradeCoins,
          adId: adResult.adId,
          adResponseId: adResult.adResponseId,
          encryptType: adResult.encryptType,
          ecpm: adResult.ecpm,
          fallbackReq: adResult.fallbackReq ?? FallbackReqType.NORMAL,
          index: waterInfo.index,
        }, true, true);

        if (result?.success) {
          setModalVisible(false);
          showToast(`恭喜获得${modalUpgradeCoins}金币`);
        } else {
          showToast('领取奖励失败，请稍后重试');
        }
      }
    } catch (error) {
      showToast('领取奖励失败，请稍后重试');
    }
  };

  // 处理点击事件
  const handlePress = () => {
    if (waterInfo.status === WaterStatus.PENDING) {
      handleNormalCheckIn();
    } else if (waterInfo.status === WaterStatus.MAKE_UP) {
      handleMakeUpCheckIn();
    }
  };

  const statusInfo = getStatusInfo();

  return (
    <>
      <TouchableOpacity
        style={styles.container}
        onPress={statusInfo.clickable ? handlePress : undefined}
        disabled={!statusInfo.clickable}
        activeOpacity={statusInfo.clickable ? 0.7 : 1}
      >
      <ImageBackground
        source={statusInfo.backgroundImage}
        style={styles.backgroundImage}
        resizeMode="contain"
      >
        <View style={styles.content}>
          <Text style={styles.title}>{waterInfo.title}</Text>

          <Text style={statusInfo.bottomTextStyle}>
            {statusInfo.bottomText}
          </Text>
        </View>

        {/* 未开始状态的蒙层 */}
        {statusInfo.showOverlay && (
          <ImageBackground
            source={statusInfo.overlayImage}
            style={styles.overlay}
            resizeMode="contain"
          >
            <Text style={styles.timeAreaText}>
              {statusInfo.timeAreaText}
            </Text>
          </ImageBackground>
        )}
      </ImageBackground>
    </TouchableOpacity>

    {/* 奖励弹窗 */}
    <DrinkWaterModal
      visible={modalVisible}
      onClose={() => {
        setModalVisible(false);
        onRefresh(); // 在弹窗关闭时刷新数据
      }}
      onPress={modalType === 'withAd' ? handleAdReward : undefined}
      coins={modalCoins}
      type={modalType}
      upgradeCoins={modalUpgradeCoins}
    />
  </>
  );
}
