import React, { useState, useEffect } from 'react';
import { View, Text, Image, TouchableOpacity, Dimensions } from 'react-native';
import { getStyles } from './styles';
import type { WaterWeek } from 'services/welfare/drinkWater';
import watchAd from 'utils/watchAd';
import useRewardGoldCoin from 'hooks/useRewardGoldCoin';
import { AD_SOURCE, RewardType, LISTEN_TASK_POSITION, FallbackReqType } from 'constants/ad';
import DrinkWaterModal from '../Modal';
import useAppStateToast from "hooks/useAppStateToast";
import xmlog from 'utilsV2/xmlog'


interface WaterWeekProps {
  waterWeek: WaterWeek;
  xmRequestId: string;
}

export default function WaterWeekCard({ waterWeek, xmRequestId }: WaterWeekProps) {
  const [localWaterWeek, setLocalWaterWeek] = useState(waterWeek);
  const { showToast } = useAppStateToast();
  const [containerWidth, setContainerWidth] = useState(0);
  
  // 弹窗状态
  const [modalVisible, setModalVisible] = useState(false);

  useEffect(() => {
    setLocalWaterWeek(waterWeek);
  }, [waterWeek]);

  const styles = getStyles();
  const { continueDrinkDay, coins, status, dayDrinkNums, title, tips, maxCoins } = localWaterWeek;
  const rewardGoldCoin = useRewardGoldCoin();

  const getProgressWidth = () => {
    const dayItemWidth = 30;
    const screenWidth = Dimensions.get('window').width;
    const containerPadding = 16;
    const trackWidth = containerWidth == 0 ? screenWidth - 4 * containerPadding - dayItemWidth
    : containerWidth - 2 * containerPadding - dayItemWidth;
    if (continueDrinkDay <= 0) {
      return 0;
    }
    if (continueDrinkDay === 1) {
      return 10;
    }
    const segmentWidth = trackWidth / 6;
    const completedSegments = Math.min(continueDrinkDay, 7) - 1;
    const progress = 10 + segmentWidth * completedSegments;
    if (continueDrinkDay == 7) {
      return progress + 10;
    }
    return progress;
  };

  const handleReward = async () => {
    if (status === 1) return; // 已领取
    xmlog.click(69119, undefined, {
      currPage: 'drinkWaterActivityPage',
      Item: '连续7天打卡奖励',
      xmRequestId,
    })
    try {
      // 1. 调用看广告方法，参照 WaterGlass.tsx 的补打卡逻辑
      const adResult = await watchAd({
        sourceName: AD_SOURCE.DRINK_WATER_WEEKLY_REWARD, // 假设的广告来源
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.DRINK_WATER_WEEKLY_REWARD, // 假设的奖励类型
        coins,
        rewardVideoStyle: 0,
      });

      if (adResult.success) {
        // 2. 发放奖励
        const result = await rewardGoldCoin({
          rewardType: RewardType.DRINK_WATER_WEEKLY_REWARD,
          sourceName: AD_SOURCE.DRINK_WATER_WEEKLY_REWARD,
          coins,
          adId: adResult.adId,
          adResponseId: adResult.adResponseId,
          encryptType: adResult.encryptType,
          ecpm: adResult.ecpm,
          fallbackReq: adResult.fallbackReq ?? FallbackReqType.NORMAL,
        },true);

        if (result?.success) {
          setLocalWaterWeek((prev) => ({ ...prev, status: 1 }));
          // 显示奖励告知弹窗
          setModalVisible(true);
        }
      }
    } catch (error) {
      showToast('领取奖励失败，请稍后重试');
    }
  };

  const renderDayItem = (dayIndex: number) => {
    const day = dayIndex + 1;
    const drinks = dayDrinkNums[dayIndex] || 0;
    const isCompleted = day <= continueDrinkDay;

    // 第七天特殊处理
    if (day === 7) {
      const isDay7Completed = continueDrinkDay >= 7 && drinks > 0;
      const canClaim = isDay7Completed && status === 0;
      const imageSource = isDay7Completed
        ? status == 1 ? require('../../../imagesV2/drink_7_coin_complete_rewarded.png'): require('../../../imagesV2/drink_7_coin_complete.png')
        : require('../../../imagesV2/drink_7_coin_uncomplete.png');
      
      return (
        <TouchableOpacity key={day} style={styles.dayContainer} onPress={handleReward} disabled={!canClaim}>
          <Image source={imageSource} style={styles.dayImage} />
          <Text style={[styles.dayText, isCompleted && styles.dayTextCompleted]}>{day}天</Text>
        </TouchableOpacity>
      );
    }

    // 前六天
    const waterLevel = Math.min(drinks, 8); // 最多8个等级
    let imageSource;
    if (isCompleted && waterLevel > 0) {
      switch (waterLevel) {
        case 1: imageSource = require('../../../imagesV2/drink_water_1.png'); break;
        case 2: imageSource = require('../../../imagesV2/drink_water_2.png'); break;
        case 3: imageSource = require('../../../imagesV2/drink_water_3.png'); break;
        case 4: imageSource = require('../../../imagesV2/drink_water_4.png'); break;
        case 5: imageSource = require('../../../imagesV2/drink_water_5.png'); break;
        case 6: imageSource = require('../../../imagesV2/drink_water_6.png'); break;
        case 7: imageSource = require('../../../imagesV2/drink_water_7.png'); break;
        case 8: imageSource = require('../../../imagesV2/drink_water_8.png'); break;
        default: imageSource = require('../../../imagesV2/drink_water_0.png'); // 未打卡时的默认空杯
      }
    } else {
        imageSource = require('../../../imagesV2/drink_water_0.png');
    }

    return (
      <View key={day} style={styles.dayContainer}>
        <Image source={imageSource} style={styles.dayImage} />
        <Text style={[styles.dayText, isCompleted && styles.dayTextCompleted]}>{day}天</Text>
      </View>
    );
  };

  return (
    <>
      <View style={styles.container}
      onLayout={e => setContainerWidth(e.nativeEvent.layout.width)}
      >
        <View style={styles.header}>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.title}>{title}</Text>
            {maxCoins? (
              <View style={styles.maxCoinsContainer}>
                <Image
                  style={styles.maxCoinsIcon}
                  source={{ uri: 'https://imagev2.xmcdn.com/storages/c00d-audiofreehighqps/3E/2E/GAqh1QQMFFVRAAAEvAO-jCTD.png' }}
                />
                <Text style={styles.maxCoinsText}>{maxCoins}</Text>
              </View>
            ) : null}
          </View>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <Text style={styles.subTitle}>已打卡</Text>
            <Text style={styles.subTitleDay}>{continueDrinkDay}</Text>
            <Text style={styles.subTitle}>天</Text>
          </View>
        </View>

        <View style={styles.daysWrapper}>
          {/* Progress Bar */}
          <View style={styles.progressBarContainer}>
            <View style={styles.progressTrack} />
            <View
              style={[
                styles.progressBar,
                { width: getProgressWidth() },
              ]}
            />
            <View style={styles.progressNodesContainer}>
              {Array.from({ length: 7 }).map((_, index) => (
                <View key={index} style={styles.progressNodeWrapper}>
                  <View style={styles.progressNode}>
                    <View
                      style={[
                        styles.progressNodeInner,
                        index < continueDrinkDay && styles.progressNodeCompleted,
                      ]}
                    />
                  </View>
                </View>
              ))}
            </View>
          </View>
          
          {/* Days */}
          <View style={styles.daysContainer}>
            {Array.from({ length: 7 }).map((_, index) => renderDayItem(index))}
          </View>
        </View>
        
        <Text style={styles.tips}>{tips}</Text>
      </View>
      
      {/* 奖励弹窗 */}
      <DrinkWaterModal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        coins={coins}
        type="normal"
        title="恭喜获得"
      />
    </>
  );
}
