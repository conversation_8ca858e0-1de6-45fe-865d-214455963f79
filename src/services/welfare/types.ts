export interface BaseRecord {
  title: string;
  createTime: string;
  coins?: string;
  amount?: string;
}

export interface GoldCoinHistoryItem extends BaseRecord {
  coins: string;
}

// 0-初始化 1-处理中 2-成功 3-失败
export enum WithdrawStatus {
  INIT = 0,
  PROCESSING = 1,
  SUCCESS = 2,
  FAILED = 3,
}

export interface WithdrawRecord extends BaseRecord {
  amount: string;
  status: WithdrawStatus;
}

export interface BannerCard {
  title: string;      // 标题，如"兑换1天vip"
  subTitle: string;   // 副标题，如"33000金币"
  coins: number;      // 兑换所需金币数
  type: number;       // 类型
  lineCoins: number;  // 划线价
  consumeType: number; // 1-金币换现金 2-金币换时长 3-金币换vip
  status: number;     // 1-可兑换 2-不可兑换
  statusImage?: string; // 状态图片链接（可选）
  buttonText?: string;  // 按钮文案
  buttonIcon?: string;  // 按钮图标链接
  statusIcon?: string;  // 状态图标链接
}

export interface GoldCoinHistoryResponse {
  success: boolean;
  code: number;
  hasMore: boolean;
  curIndex: string;
  transactionType: number;
  list: GoldCoinHistoryItem[];
}

export interface QueryGoldCoinPageResponse {
  success: boolean;
  code: number;
  coins: number;
  enableWithDraw: boolean;
  bannerCards: BannerCard[];
  goldCoinHistory: GoldCoinHistoryResponse;
}

export interface ExchangeResponse {
  success: boolean;
  code: number;
  retry: boolean;
  toast: string;
}

export interface CashFlowItem {
  id: number;
  type: number;
  date: string;
  comment: string;
  delta: string;
  createdAt: string;
  updatedAt: string;
  withdrawStatus: number;
}

export interface CashFlowResponse {
  ret: number;
  msg: string | null;
  data: CashFlowItem[];
}