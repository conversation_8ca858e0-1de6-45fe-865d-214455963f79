import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from 'servicesV2/request';

// export type TaskStatus = 0 | 1 | 2; // 0: 未完成, 1: 待领取, 2: 已领取

export enum ShareCoinsTaskDayStatus {
  UNFINISHED = 0, // 未完成
  PENDING = 1, // 完成中
  DONE = 2, // 已完成
  COMPLEMENT = 3, // 补签
}

export enum ShareCoinsTaskStatus {
  PENDING = 1,
  FAILED = 2,
  DONE = 3,
}

export interface ShareCoinsModalInfo {
  shareCoinsModalTitle: string,
  shareCoinsModalSubTitle: string,
  incentiveLightIcon: string,
  incentiveDarkIcon: string,
  dailyCoinsAwardDetail: DailyCoinsAwardDetail[],
}

export enum AwardStatus {
  CAN_GET = 0,
  GOT = 1,
  CANNOT_GET = 2
}

export interface DailyCoinsAwardDetail {
  stepNo: number;
  amount: string;
  amountText: string;
  status: AwardStatus;
  supriseAward: boolean;
  today: boolean;
}

export interface CarveUpAwardInfo {
  success: boolean,         // 是否成功
  failCode: number,            //失败code，成功为200
  taskStatus: ShareCoinsTaskStatus, // 1 -- 任务进行中， 2--任务失败 3--任务已完成
  buttonText: string, // 按钮文案
  minButtonText: string,
  enableReward: boolean, // 是否可以领取
  dayInfo: {
    label: string,
    status: ShareCoinsTaskDayStatus, // 0 -- 未完成，1 -- 待领取，2 -- 已领取
    totalTimes?: number,
    progress?: number,
    text: string,
  }[]
  progress: number, // 任务进度 0-7, 
  dayOfWeek: number, // 当前是周几
  // 副标题，高亮{{}}这个符号包裹的文案
  subTitle: string,
  alreadyRewardTimes: number,
  maxRewardTimes: number,
  replenishSignInBtnText: string, 
  replenishSignInMinBtnText: string,
  replenishSignIn: {
    replenishSignInDays: number,
    totalTimes: number,
    alreadyTimes: number,
    btnText: string,
    minBtnText: string
  } | null,
  detail: {
    day: number,
    status: number,
  }[],
  shareCoinsModalInfo: ShareCoinsModalInfo,
}

export const queryCarveUpAward = async (): Promise<ResDataType<CarveUpAwardInfo> | undefined> => {
  return request<CarveUpAwardInfo>({
      ...API_ADSE,
      url: `incentive/ting/welfare/queryCarveUpAward/ts-${Date.now()}`,
    });
};
