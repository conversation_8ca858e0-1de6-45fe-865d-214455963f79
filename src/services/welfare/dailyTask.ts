/**
 * 【换量任务功能】每日任务服务
 *
 * 项目作用：福利中心换量任务功能 - 每日任务数据服务
 *
 * 主要功能：
 * - queryDailyTask: 获取每日任务列表（包含换量任务）
 * - DailyTaskType: 每日任务类型枚举（新增EXCHANGE换量任务类型）
 * - ExchangeTaskStatus: 换量任务状态枚举（未完成/已完成/已领取）
 * - ExchangeTaskButtonState: 换量任务按钮状态枚举
 * - DailyTaskItem: 每日任务数据结构（扩展换量任务字段）
 */

import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from '../../servicesV2/request';

export enum DailyTaskType {
  RED_PACKET_RAIN = 'RED_PACKET_RAIN',
  TREASURE_BOX = 'TREASURE_BOX',
  PLAY_LET = 'PLAY_LET',
  MARKET = 'MARKET',
  EXCHANGE = 'EXCHANGE', // 【换量任务功能】换量任务类型
  DRINK_WATER = 'DRINK_WATER'
}

// 【换量任务功能】换量任务状态枚举
export enum ExchangeTaskStatus {
  UNFINISHED = 0, // 未完成 - 显示"去完成"
  FINISHED = 1,   // 已完成 - 显示"领取"
  CLAIMED = 2     // 已领取 - 显示"已领取"
}

// 【换量任务功能】换量任务按钮状态
export enum ExchangeTaskButtonState {
  GO_COMPLETE = 'go_complete',
  CLAIM = 'claim',
  CLAIMED = 'claimed'
}

export interface DailyTaskItem {
  positionId: number;
  positionName: string;
  title: string;
  subTitle: string;
  coins: number;
  extMap: string;
  calmSeconds: number;
  btnText: string;
  type?: DailyTaskType;

  // 【换量任务功能】换量任务扩展字段
  taskId?: number; // 换量任务ID
  status?: ExchangeTaskStatus; // 换量任务状态
  contextMap?: Record<string, string>; // 按钮文案映射
  guideLink?: string; // 跳转链接
  h5Link?: string; // H5跳转链接
  schemaLink?: string; // Schema跳转链接
  rta?: boolean; // 是否为RTA类型任务
  logo?: string; // 任务图标
  desc?: string; // 任务描述
  icon?: string;
  darkIcon?: string;
  maxAmount?: number;
}

interface DailyTaskResult {
  success: boolean;
  code: number;
  list: DailyTaskItem[];
}

/**
 * 【换量任务功能】获取每日任务列表
 * 包含传统每日任务和新增的换量任务
 * 
 * URL参数说明：
 * - drinkwater=1: 支持喝水任务
 * - exchange=1: 支持换量任务（用于版本控制，避免低版本客户端收到换量任务）
 * 
 * @returns Promise<ResDataType<DailyTaskResult>> 每日任务列表数据
 */
export const queryDailyTask = async (): Promise<ResDataType<DailyTaskResult>> => {
  const response = await request<DailyTaskResult>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryDailyTask/ts-${Date.now()}?drinkwater=1&exchange=1`,
  });

  return response as ResDataType<DailyTaskResult>;
};