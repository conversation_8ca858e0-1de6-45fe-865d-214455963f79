import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from '../../servicesV2/request';
import userInfoDetail from 'modulesV2/userInfoDetail';
import { encryptByType, getXuidTicket } from 'utilsV2/native';
import { XUID_ticketConfig } from 'constantsV2';
import uuid from 'utilsV2/uuid';
import { AD_SOURCE, RewardType, NonCoinRewardType } from 'constants/ad';
import { FallbackReqType } from 'constants/ad';



interface WelfareResponse {
  success: boolean;
  code: number;
  tabs: string[];
  activateTab: 'COINS' | 'POINTS';
  drawInfo: {
    enableCash: boolean;
    cash: number;
    coins: number;
  };
  coinTips: {
    icon: string;
    text: string;
  };
}

export const queryDrawInfo = async (): Promise<ResDataType<WelfareResponse> | undefined> => {
  const ticket = await getXuidTicket({
    businessId: XUID_ticketConfig.cashTask.businessId,
    scene: XUID_ticketConfig.cashTask.scene,
    uid: userInfoDetail.getDetail().uid || -1,
  })
  return request<WelfareResponse>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryDrawInfo/ts-${Date.now()}?ticket=${ticket}`,
  });
};

interface RandomPopDataResponse {
  success: boolean;
  valid: boolean;
  rewardGoldCoins: number;
  code: number;
  msg: string;
}

/**
 * 随机弹窗配置
 * @returns 随机弹窗配置
 */
export const queryRandomPopData = async (): Promise<ResDataType<RandomPopDataResponse> | undefined> => {
  const uid = userInfoDetail.getDetail().uid || -1;
  return request<RandomPopDataResponse>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryRandomPopData/ts-${Date.now()}?uid=${uid}`,
  });
};

interface CommonConfigResponse {
  success: boolean;
  code: number;
  msg: string;
  goldClearRule: string;   // 金币清零规则
  randomPopConfigData: {  // 随机弹窗配置
    enable: boolean;
    remainSeconds: number;
    intervalSeconds: number;
    totalTimes: number;
    text: string;
  };
  taskKeys: string[];
  popModalConfig?: {
    //maxShowModalCount: number,
    modalConfig: PopModalConfig[]
  };
}

interface PopModalConfig {
  id: string;
  priority: number;
  enable: boolean;
  maxShowTimesPerDay: number;
}

/**
 * 福利页全局通用配置
 * @returns 福利页全局通用配置
 */
export const queryCommonConfig = async (srcChannel: string): Promise<ResDataType<CommonConfigResponse> | undefined> => {
  const uid = userInfoDetail.getDetail().uid || -1;
  return request<CommonConfigResponse>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryCommonConfig/ts-${Date.now()}?uid=${uid}&srcChannel=${srcChannel}`,
  });
};

/**
 * 上报组件曝光
 * @param modalId 组件ID
 * @param traceId 曝光ID
 * @returns 
 */
export const reportShowModal = async (modalId: string, traceId: number): Promise<ResDataType<void> | undefined> => {
  const uid = userInfoDetail.getDetail().uid || -1;
  return request<void>({
    ...API_ADSE,
    url: `incentive/ting/reportComponentExposure/ts-${Date.now()}`,
    option: {
      method: 'post',
      data: JSON.stringify({
        uid,
        componentId: modalId,
        traceId,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    }
  });
};

export interface RewardGoldCoinParams {
  rewardType: RewardType | NonCoinRewardType;
  coins: number;
  sourceName: AD_SOURCE;
  retry?: number;
  extMap?: string;
  adId?: number;
  adResponseId?: number;
  ecpm?: string;
  encryptType?: string;
  fallbackReq: FallbackReqType; // 1-未正常曝光，异常兜底发放的奖励； 0-正常曝光完成任务发放奖励
  isNoCoin?: boolean // 是否不需要发放金币奖励（补签情况下）
  taskId?: number; // 【换量任务功能】任务ID，用于换量任务奖励发放
  index?: number; // 新增 index 字段，打卡等场景用
}

interface RewardGoldCoinRequestParams {
  adId?: number;
  adResponseId?: number;
  coins: number;
  ecpm?: string;
  encryptType?: string;
  extMap?: string;
  fallbackReq: FallbackReqType; // 1-未正常曝光，异常兜底发放的奖励； 0-正常曝光完成任务发放奖励
  requestId: string;
  retry?: number;
  rewardType: RewardType | NonCoinRewardType;
  signature: string;
  sourceName: AD_SOURCE;
  ticket: string;
  ts: number;
  taskId?: number; // 【换量任务功能】任务ID，用于换量任务奖励发放
  index?: number;
}

export interface RewardGoldCoinResult {
  success: boolean;
  code: number;
  retry: boolean; // 是否重试
  toast: string;
  coins: number; // 领取的金币数
  upgradeCoins: number; // 可升级奖励
  adAgainCoins: number; // 再看一个奖励
}

const MAX_RETRY_COUNT = 3;

export const rewardGoldCoin = async (params: RewardGoldCoinParams, retryCount = 0): Promise<ResDataType<RewardGoldCoinResult>> => {
  const { rewardType, sourceName, coins = 0, ecpm = '', encryptType = '', fallbackReq = 0, isNoCoin = false, index = 0 } = params;
  const uid = userInfoDetail.getDetail().uid || -1;
  let ticket = '';
  if (!isNoCoin) {
    ticket = await getXuidTicket({
      businessId: XUID_ticketConfig.coinTask.businessId,
      scene: XUID_ticketConfig.coinTask.scene,
      uid,
    });
  }
  
  const requestId = uuid();
  const ts = Date.now();
  const { checkData: signature } = await encryptByType('md5', {
    checkData: `${requestId}&${uid}&${rewardType}&${coins}&${ticket}&${ecpm}&${encryptType}&${ts}&${retryCount}&${XUID_ticketConfig.coinTask.salt}`
  });

  const { checkData: nonCoinsignature } = await encryptByType('md5', {
    checkData: `${requestId}&${uid}&${ecpm}&${rewardType}&${encryptType}&${ts}&${retryCount}&${XUID_ticketConfig.coinTask.salt}`
  });

  const data: RewardGoldCoinRequestParams = {
    requestId,
    ts,
    coins,
    sourceName,
    rewardType,
    ticket,
    signature: isNoCoin ? nonCoinsignature : signature,
    retry: retryCount,
    fallbackReq,
    index
  };

  const assignProps = ['extMap', 'adId', 'adResponseId', 'ecpm', 'encryptType', 'taskId', 'index'];
  Object.assign(data,
    Object.fromEntries(
      assignProps
        .filter(key => params[key as keyof RewardGoldCoinParams] !== undefined)
        .map(key => [key, params[key as keyof RewardGoldCoinParams]])
    )
  );

  try {
    const response = await request<RewardGoldCoinResult>({
      ...API_ADSE,
      url: isNoCoin ? `incentive/ting/welfare/rewardNonGoldCoins/ts-${ts}` : `incentive/ting/welfare/rewardGoldCoin/ts-${ts}`,
      option: {
        method: 'post',
        data: JSON.stringify(data),
        headers: {
          'Content-Type': 'application/json',
        },
      }
    });

    if (response?.data?.retry && retryCount < MAX_RETRY_COUNT) {
      // await sleep(RETRY_DELAY);
      return rewardGoldCoin(params, retryCount + 1);
    }

    return response as ResDataType<RewardGoldCoinResult>;
  } catch (error) {
    if (retryCount < MAX_RETRY_COUNT) {
      // await sleep(RETRY_DELAY);
      return rewardGoldCoin(params, retryCount + 1);
    }
    throw error;
  }
};

export * from './dailyTask';
export * from './detail';
export * from './adTask';
export * from './flipCardTask';
export * from './signIn';
export * from './exchangeTask'; // 【换量任务功能】导出换量任务相关工具函数