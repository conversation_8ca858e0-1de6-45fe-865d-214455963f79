import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from '../../servicesV2/request';

// 喝水状态枚举
export enum WaterStatus {
  PENDING = 1,    // 待打卡
  NOT_STARTED = 2, // 未开始
  COMPLETED = 3,   // 已打卡
  MAKE_UP = 4,     // 可补打卡
}

// 单杯水信息
export interface WaterInfo {
  index: number;
  title: string;
  timeArea: string;
  coins: number;
  status: WaterStatus;
}

// 喝水提醒信息
export interface WaterTip {
  title: string;
  subTitle: string;
  icon: string;
}

// 连续7天打卡奖励模块
export interface WaterWeek {
  continueDrinkDay: number; // 连续打卡的天数
  maxCoins: string | null; // 连续7天打卡奖励的最大金币数
  coins: number; // 连续7天打卡奖励的金币数
  status: number; // 0-未领取，1已领取
  dayDrinkNums: number[]; // 每天打卡的次数
  title: string;
  tips: string;
}

// 连续7天满勤奖励模块
export interface WaterFullWeek {
  continueDrinkDay: number; // 连续满勤打卡的天数
  maxCoins: string | null; // 连续7天满勤打卡奖励的最大金币数
  coins: number; // 连续7天满勤打卡奖励的金币数
  status: number; // 0-未领取，1已领取
  title: string;
  tips: string;
}

// 查询喝水信息响应
export interface QueryWaterInfoResponse {
  title: string;
  rulePage:string,
  coldTime: number, // 补卡冷却时间，单位：秒
  waterInfo: WaterInfo[];
  waterTip: WaterTip;
  waterWeek: WaterWeek;
  waterFullWeek: WaterFullWeek;
  success: boolean;
  code: number;
  msg: string | null;
}

/**
 * 查询八杯水及喝水提醒模块数据
 */
export const queryWaterInfo = async (): Promise<ResDataType<QueryWaterInfoResponse> | undefined> => {
  return request<QueryWaterInfoResponse>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryWaterInfo/ts-${Date.now()}`,
  });
};
