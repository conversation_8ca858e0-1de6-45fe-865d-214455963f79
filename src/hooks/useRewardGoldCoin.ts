import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import { updateWelfareAtom } from "atom/welfare";
import { NativeModules } from 'react-native'
import { rewardGoldCoin, RewardGoldCoinParams, RewardGoldCoinResult } from "services/welfare";
import { FallbackReqType } from "constants/ad";
import useDebounceUpdate from "./useDebounceUpdate";
import customReportError from "utilsV2/customReportError";
import useAppStateToast from "./useAppStateToast";

const AdModule = NativeModules?.AdModule

import { hapticSuccess } from "utils/haptic";

export default function useRewardGoldCoin() {
  const updateWelfare = useSetAtom(updateWelfareAtom);
  const debouncedUpdateWelfare = useDebounceUpdate(updateWelfare);

  // 使用自定义 hook 获取 showToast 方法，无需关心前后台状态
  const { showToast } = useAppStateToast();

  async function reward(
    params: RewardGoldCoinParams,
    customRewardHint?: boolean,
    customFailHint?: boolean,
  ): Promise<RewardGoldCoinResult> {
    try {
      if(params.adId || params.fallbackReq === FallbackReqType.FALLBACK) {
        AdModule?.adRealTimeWithParams?.({
          responseId: params.adResponseId,
          positionId: 307,
          adItemId: params.adId,
          sourceName: params.sourceName,
          rewardType: params.rewardType,
          logType: "requestReward",
          action:"request",
          fallbackReq: params.fallbackReq,
          fallBackReq: params.fallbackReq
        })
      }
      console.log('rewardGoldCoin request params', params)
      const result = await rewardGoldCoin(params);
      if(params.adId || params.fallbackReq === FallbackReqType.FALLBACK) {
        AdModule?.adRealTimeWithParams?.({
          responseId: params.adResponseId,
          positionId: 307,
          adItemId: params.adId,
          sourceName: params.sourceName,
          rewardType: params.rewardType,
          logType: "requestReward",
          action:"result",
          success: result?.data?.success ? 1 : 0,
          errorMsg: result.msg,
          fallbackReq: params.fallbackReq,
          fallBackReq: params.fallbackReq
        })
      }
      console.log('rewardGoldCoin request result', result)
      if (result?.data?.success) {
        const coins = result.data.coins;
        debouncedUpdateWelfare();
        hapticSuccess();

        // 如果传入了自定义奖励回调，则使用自定义通知，否则使用toast
        if (!customRewardHint) {
          showToast(`恭喜获得${coins}金币${coins > 3000 ? '！' : ''}`);
        }
      } else if (result?.data?.toast) {
        if(!customFailHint) {
          showToast(result.data.toast);
        } 
      }
      return result?.data;
    } catch (error) {
      customReportError({
        source: 'rewardGoldCoin',
        error: error,
      });
      customReportError({
        source: 'rewardGoldCoin_params',
        error: JSON.stringify(params),
      });
      showToast('奖励领取失败，请稍后重试');
      throw error;
    }
  }

  return reward;
} 