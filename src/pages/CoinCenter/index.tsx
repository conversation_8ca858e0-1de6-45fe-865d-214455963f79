import React, { useEffect, useRef, useState, useContext } from "react";
import { Animated, View, Platform, NativeModules } from "react-native"
import { useAtomValue, useAtom, useSetAtom } from "jotai";
import { getStyles } from "./style";
// import nativeInfoModule from '../../modulesV2/nativeInfoModule'
import Balance from "components/CoinCenter/Balance";
import SignIn from "components/CoinCenter/SignIn";
// import AdPopWindow from "components/CoinCenter/AdPopWindow";
import PopWindow from "components/CoinCenter/DailyTask/PopWindow";
import AdPopWindowForVideo from "components/CoinCenter/AdPopWindowForVideo";
import ValuableTask from "components/CoinCenter/ValuableTask";
import VideoTask from "components/CoinCenter/VideoTask";
import FlipCard from "components/CoinCenter/FlipCardTask";
import DailyTask from "components/CoinCenter/DailyTask";
import ListenTask from "components/CoinCenter/ListenTask";
import ShareCoins from 'components/CoinCenter/ShareCoins'
// import ShareCoinsModal from 'components/CoinCenter/ShareCoinsModal'
import ShareCoinsCollapse from 'components/CoinCenter/ShareCoins/ShareCoinsCollapse'
// import ShareCoinsRewardModal from 'components/CoinCenter/ShareCoins/ShareCoinsRewardModal'
import AnnouncementBanner from "components/CoinCenter/AnnouncementBanner";
import ModalManager from 'components/common/ModalManager';
import CheckInCollapse from "components/CoinCenter/CheckInCollapse";
import ListenTaskModal from 'components/CoinCenter/ListenTask/ListenTaskModal'
import coinCenterThemeAtom from './theme';
import { BetterImage } from "@xmly/rn-components";
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProps } from "router/type";
import { scrollValueAtom } from './store/scroll';
import Header from "components/CoinCenter/Header";
import { isHarmony } from "../../../rnEnv";
import { NativeInfoContext } from 'contextV2/nativeInfoContext'
import Rule from 'components/CoinCenter/Rule';
import { usePageReport } from "hooks/usePageReport";
import { StackScreenProps } from "@react-navigation/stack";
import { RootStackParamList } from "router/type";
import { ScrollAnalyticWapper, ScrollEventSender } from "@xmly/react-native-page-analytics";
import { updateWelfareAtom } from "atom/welfare";
import { PageEventEmitter } from "defs";
import RedPacketRain from 'components/CoinCenter/RedPacketRain';
import { showRedPacketRainAtom } from 'atom/redPacketRain';
// import RedPacketRain from 'components/CoinCenter/RedPacketRain/demo';
import GlobalEventEmitter from "utilsV2/globalEventEmitter";
// import NotificationPopup from 'components/CoinCenter/NotificationPopup';
import TouchTask from "components/CoinCenter/TouchTask";
import { ScrollEventName } from "./constants";
import { writeCommonConfigAtom } from "./store/commonConfig";
import { commonConfigAtom } from "./store/commonConfig";

// 全局标记，用于记录是否已经执行过自动跳转（放在组件外部，避免组件重建时重置）
let hasAutoNavigatedToDrinkWater = false;

const coinCenterId = 'coinCenterContent';

const componentMap = {
  ShareCoins,
  ValuableTask,
  ListenTask,
  VideoTask,
  FlipCard,
  DailyTask
};

export default function CoinCenter(props: StackScreenProps<RootStackParamList>) {
  const theme = useAtomValue(coinCenterThemeAtom);
  const styles = getStyles(theme);
  const navigation = useNavigation<RootNavigationProps>()
  const [scrollValue] = useAtom(scrollValueAtom);
  const [headerHeight, setHeaderHeight] = useState(0);
  const nativeInfo = useContext(NativeInfoContext);
  const updateWelfare = useSetAtom(updateWelfareAtom);
  const showRedPacketRain = useAtomValue(showRedPacketRainAtom);

  const commonConfig = useAtomValue(commonConfigAtom);
  const setCommonConfig = useSetAtom(commonConfigAtom);
  const queryCommonConfig = useSetAtom(writeCommonConfigAtom);

  const [showSignModal, setShowSignModal] = useState(true);
  const [popVisible, setPopVisible] = useState(false);
  const [rewardCoin, setRewardCoin] =useState<number>(0);
  const VideoModalRef = useRef(null);
  const VideoRef = useRef(null);

  usePageReport({
    pageViewCode: 67685,
    pageExitCode: 67686,
    currPage: 'welfareCenter',
    params: {
      from: nativeInfo?.srcChannel ?? 'unknown',
      sourceType: nativeInfo?.srcChannel ?? 'unknown'
    },
    otherProps: props
  });

  const handleScroll = Animated.event([{ nativeEvent: { contentOffset: { y: scrollValue } } }], {
    useNativeDriver: true,
    listener: (event: any) => {
      ScrollEventSender.send(coinCenterId, 'scroll')
      GlobalEventEmitter.emit(ScrollEventName.onScroll);
    }
  });

  useEffect(() => {
    // 去掉骨架屏
    GlobalEventEmitter.emit('appContentReady');

    // iOS设备：如果是通过 route=drinkWater 进入的，立即自动跳转到喝水页面
    if (Platform.OS === 'ios' && nativeInfo?.route === 'drinkWater' && !hasAutoNavigatedToDrinkWater) {
      hasAutoNavigatedToDrinkWater = true;
      // 使用requestAnimationFrame确保在下一帧执行导航
      requestAnimationFrame(() => {
        navigation.push('DrinkWater'); // 使用push而不是navigate，确保导航栈的正确性
      });
    }

    navigation.addListener('blur', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(false)
      }
    });

    navigation.addListener('focus', () => {
      if (Platform.OS == 'ios') {
        NativeModules.CompatibleIOS.setSwipBackGestureEnable(true)
      }
    });

    const resumeListener = PageEventEmitter.addListener('onResume', () => {
      console.log("onResuming....................")
      updateWelfare();
    });

    return () => {
      resumeListener?.remove();
    }
  }, []);

  useEffect(() => {
    // 1. 先重置为 loading: true
    setCommonConfig({ ...commonConfig, loading: true });
    // 2. 再查询配置
    queryCommonConfig(nativeInfo?.srcChannel ?? 'unknown');

    return () => {
      setCommonConfig(prev => ({ ...prev, loading: true }));
    }
  }, []);


  function navigateToHome() {
    GlobalEventEmitter.emit('appContentLoading')
    navigation.replace('Home')
  }

  function handleScrollBeginDrag() {
    GlobalEventEmitter.emit(ScrollEventName.onScrollBeginDrag);
  }

  function handleScrollEndDrag() {
    GlobalEventEmitter.emit(ScrollEventName.onScrollEndDrag);
  }

  function handleMomentumScrollBegin() {
    GlobalEventEmitter.emit(ScrollEventName.onMomentumScrollBegin);
  }

  function handleMomentumScrollEnd() {
    GlobalEventEmitter.emit(ScrollEventName.onMomentumScrollEnd);
  }
  const onRewardCoinFinish = (coin:number)=>{
    setRewardCoin(coin);
    setPopVisible(true);
  }
  const onFinishVideo = (cadId?: number | undefined, adResponseId?: number, rewardCoin?: number, fallbackReq?:number,ecpm?: string) => {
    (VideoModalRef?.current as any)?.finishVideo(cadId, adResponseId, rewardCoin,fallbackReq,ecpm);
  }

  const onNextVideo = () => {
    (VideoRef?.current as any)?.nextVideo();
  }

  const onFinishVidoeReward= () => {
    (VideoRef?.current as any)?.finishVidoeReward();
  }


  return (
    <View style={[styles.container]}>
      <Header
        onTabPress={navigateToHome}
        onLayout={setHeaderHeight}
        singleTab={isHarmony}
        hideTitle={nativeInfo?.embed}
      />
      <ScrollAnalyticWapper
        id={coinCenterId}
        viewStyle={{ flex: 1 }}
        //@ts-ignore
        useNavigation={useNavigation}
      >
        <Animated.ScrollView
          style={[styles.scrollView]}
          contentContainerStyle={styles.scrollContent}
          onScroll={handleScroll}
          onScrollBeginDrag={handleScrollBeginDrag}
          onScrollEndDrag={handleScrollEndDrag}
          onMomentumScrollBegin={handleMomentumScrollBegin}
          onMomentumScrollEnd={handleMomentumScrollEnd}
          scrollEventThrottle={16}
          showsVerticalScrollIndicator={false}
          bounces={false}
        >
          <BetterImage
            source={{
              uri: theme.headerBg
            }}
            style={styles.headerBg}
          />
          <View style={{ height: headerHeight }}></View>
          <AnnouncementBanner />

          <View style={styles.content}>
            <Balance showSignModal={showSignModal} />
            {showSignModal? (<BetterImage
              source={{
                uri: theme.splitLine
              }}
              imgWidth={343}
              imgHeight={9}
              style={styles.splitLine}
            />) : null}
            <SignIn setShowSignModal={setShowSignModal} />
          </View>

          <View>
            {
              commonConfig.taskKeys.map(key => {
                const Comp = componentMap[key as keyof typeof componentMap];
                return Comp ? <Comp  onFinishVideo={onFinishVideo} key={key} onRewardCoinFinish={onRewardCoinFinish}/> : null;
              })
            }
          </View>
          <CheckInCollapse />
          <ShareCoinsCollapse />
          <Rule />
        </Animated.ScrollView>
      </ScrollAnalyticWapper>

      <ModalManager />

      <PopWindow rewardCoin={rewardCoin} popVisible={popVisible} onClose={() => setPopVisible(false)}/>
      <AdPopWindowForVideo onFinishVidoeReward={onFinishVidoeReward} onNextVideo={onNextVideo} ref={VideoModalRef} />
      <ListenTaskModal visible={true} onClose={() => {}} type="withAd" />
      <RedPacketRain visible={showRedPacketRain} />
      <TouchTask />
    </View>
  )
}