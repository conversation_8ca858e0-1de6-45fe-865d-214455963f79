import { atom } from 'jotai';
import { queryCommonConfig, queryRandomPopData } from 'services/welfare';

interface CommonConfigInfo {
    goldClearRule: string;   // 金币清零规则
    randomPopConfigData: {  // 随机弹窗配置
        enable: boolean;
        remainSeconds: number;
        intervalSeconds: number;
        totalTimes: number;
        text: string;
    };
    taskKeys: string[];
    popModalConfig?: {
      //maxShowModalCount: number,
      modalConfig: PopModalConfig[] | undefined
    };
    loading: boolean;
}

interface PopModalConfig {
    id: string;
    priority: number;
    enable: boolean;
    maxShowTimesPerDay: number;
}

interface RandomPopDataInfo {
    success: boolean;
    valid: boolean;
    rewardGoldCoins: number;
    code: number;
    msg: string;
}

export const defaultCommonConfig: CommonConfigInfo = {
  goldClearRule: '连续30天没来，金币会清空哦~',
  randomPopConfigData: {
    enable: false,
    remainSeconds: 0,
    intervalSeconds: 0,
    totalTimes: 0,
    text: '',
  },
  taskKeys: [
    "ShareCoins",
    "ValuableTask",
    "ListenTask",
    "VideoTask",
    "FlipCard",
    "DailyTask"
  ],
  popModalConfig: {
    modalConfig: undefined
  },
  loading: true,
};

export const commonConfigAtom = atom<CommonConfigInfo>(defaultCommonConfig);

export const writeCommonConfigAtom = atom(
  null,
  async (get, set, srcChannel: string) => {

    let config = defaultCommonConfig;
    try {
      const commonConfig = await queryCommonConfig(srcChannel);
      if (
        commonConfig?.ret === 0 &&
        commonConfig.data.success &&
        Array.isArray(commonConfig.data.taskKeys) &&
        commonConfig.data.taskKeys.length > 0
      ) {
        config = { ...commonConfig.data, loading: false };
      }
    } catch (e) {
      // 可以加日志
    }
    console.log('------- writeCommonConfigAtom -------->', config);
    set(commonConfigAtom, { ...config, loading: false });
  }
);

export const randomPopDataAtom = atom<RandomPopDataInfo | null>(null);

export const writeRandomPopDataAtom = atom(
  null,
  async (get, set) => {
    const randomPopData = await queryRandomPopData();
    if (randomPopData?.ret === 0 && randomPopData.data.success) {
        set(randomPopDataAtom, randomPopData.data);
    }
  },
);

