import { StackNavigationProp, StackScreenProps } from '@react-navigation/stack'

// 页面跳转参数
export type RootStackParamList = {
  Home: undefined
  CoinCenter: undefined
  PosterHistory: undefined
  CreditDetail: undefined
  TaskList: undefined
  CommodityList: undefined
  CoinDetail: undefined
  RedPacketRain: undefined
  DrinkWater: undefined
}

export type RootNavigationProps = StackNavigationProp<RootStackParamList>

export type RootScreenProps<T> = StackScreenProps<RootStackParamList, T>
