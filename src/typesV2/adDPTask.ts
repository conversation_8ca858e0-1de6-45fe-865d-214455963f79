export interface BidSlotList { }

export interface BusinessExtraInfo {
  adUniqId: string,
  bannerMiniProgram: boolean,
  bannerLiveBroadcast: boolean
}

export enum AdAppTaskStatus {
  Default = 0, // 默认状态
  Clicked, // 点击过
  Settling, // 结算中
  Finished, // 领奖励
}

export interface AdDPTaskItem {
  adUniqId: number
  bonusPoints: number
  shareData?: any
  isShareFlag: boolean
  thirdStatUrl?: any
  thirdShowStatUrls: any[]
  thirdClickStatUrls: any[]
  showTokens: any[]
  clickTokens: any[]
  showTokenEnable: boolean
  clickTokenEnable: boolean
  recSrc: string
  recTrack: string
  link: string
  realLink: string
  dpRealLink: string
  thirdDpArouseUrl?: any
  thirdDpArouseFailUrl?: any
  adMark: string
  isLandScape: boolean
  isInternal: number
  positionId: number
  bucketIds: string
  adpr?: any
  clickType: number
  adBucketIds?: any
  chargeMethod: string
  dspPositionId?: any
  wxMiniProgramId: string
  buttonIconUrl?: any
  iconAnimation: number
  enableShowProcessButton: boolean
  enableContinuePlay: boolean
  enableVideoJoinAppStore: boolean
  providerAvatar?: any
  providerName?: any
  landingPageResId?: any
  landingPageDomainName?: any
  animationType: number
  animationMaterialList: any[]
  itingType: number
  isEffectiveExposure: boolean
  chargeTime?: any
  isNeedPreRequest: boolean
  preRequestEffectTime: number
  inScreenSource: number
  materialProvideSource: string
  downloadAppLogo?: any
  downloadAppName: string
  downloadAppDesc?: any
  appPackageName: string
  needDownloadProgressBar: boolean
  needRender: boolean
  renderWidth: number
  renderHeight: number
  adDownloaderType: number
  downloadProgressBarClickType: number
  downloadMonitorMoment: number
  jumpOpti: number
  commonReportMap: string
  downloadPopupStyle: number
  enableDownloadPopUp: boolean
  downloadPopUpClickArea: number
  enableOriginality: boolean
  downloadLink?: any
  xarea: number
  yarea: number
  videoDurationTime: number
  slotRealBid: boolean
  slotAdm?: any
  winPrice?: any
  shouldXmClick: boolean
  shouldRealLink: boolean
  dpRetrySecond: number
  showMonitorDelaySec: number
  jumpTrackId: number
  autoJumpTime: number
  openBounce: number
  bounceDelay: number
  goldCoinsNum: number
  goldCoinsNumEncrypt?: any
  businessExtraInfo: BusinessExtraInfo
  planId: number
  rankLevel: number
  isMobileRtb: boolean
  priceEncrypt: string
  bidMinPrice?: any
  thirdAdType: number
  hideInfoForWebUa: boolean
  cycleShowSize: number
  enableShake: boolean
  adid: number
  cover: string
  showstyle: number
  name: string
  description: string
  scheme?: any
  linkType: number
  displayType: number
  openlinkType: number
  loadingShowTime: number
  apkUrl?: any
  adtype: number
  auto: boolean
  adUserType: string
  visiblePartyClick: boolean
  videoCover?: any
  dynamicCover?: any
  soundUrl?: any
  logo?: any
  clickTitle: string
  advertiserName?: any
  volume: number
  isTrueExposure: boolean
  adShowTime: number
  adIntervalTime: number
  clickableAreaType: number
  enableAnchorRec: boolean
  adVolumeGain: number
  ventPositionTime: number
  insertSoundOrder: number
  bgColor: string
  chaseExpireTime: number
  upperText: string
  buttonText: string
  backupCover?: any
  monopolize: boolean
  comboAd: boolean
  dsp: boolean
  lbs: boolean
  price: number
  dpMarketUrl?: string // 据此判断是否是拉新任务
  priceSecondBanner: number // 最高X元
  taskStatus?: AdAppTaskStatus // 任务状态
  fromCache?: boolean // 是否来自缓存
  highModeExpireTime?: number // 缓存过期时间
}

export interface AdDPTaskListRequestResult {
  bidSlotList: BidSlotList
  rtbType: number
  responseId: number
  ret: number
  clientIp: string
  data: AdDPTaskItem[]
  adTypes: number[]
  source: number
}
