export type NativeInfo = {
  channelTaskId?: string
  bundle: string
  tab?: string
  rootTag: number
  initData: NativeInfoInitData
  drawCoin?: string // 来自播放页的领积分入口，进入后需自动领积分，并领取成功弹窗
  srcChannel?: string // 来源
  channelName?: string // 第三方名称
  task?: string // 第三方任务id
  token?: string // 第三方token
  pcTask?: string // pc任务弹窗
  route?: string // 指定路由
  toTaskId?: string // 定位到指定任务
  action?: string // 指定行为
  toCashTask?: boolean // 定位到现金任务
  embed?: string // 是否嵌套
  coin?: string // 是否是金币中心

  // 【换量任务功能】扩展字段 - 支持完整的deeplink参数
  msg_type?: string // 消息类型 (如: "94")
  cid?: string // 渠道ID (实际字段名)
  channelid?: string // 渠道标识 (小写)
  is_growth_exchange_welfare?: string // 福利中心换量标识 ("true"/"false")
  originalDeeplink?: string // 【重要】原始的完整deeplink URL，用于直接base64编码上报
}

export type NativeInfoInitData = {
  account: { isLogin: boolean; uid?: number; isVip?: boolean }
  apiVersion: string
  bundleVersion: string
  env: NativeInfoInitDataEnv
  isChildProtectOpen?: boolean
  isDarkMode?: boolean // 是否暗黑模式
  isNewInstall?: boolean
  isNewVersion?: boolean
}

export type NativeInfoInitDataEnv = {
  envType: number
  supportFullScreen?: boolean
}
